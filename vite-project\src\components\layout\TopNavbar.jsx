import React from 'react';
import { Search, Plus, Bell, Menu } from 'lucide-react';

const TopNavbar = ({ onMenuClick }) => {
  return (
    <header className="fixed top-0 left-0 right-0 bg-white shadow-sm border-b border-gray-200 z-30">
      <div className="flex items-center justify-between px-4 py-3 md:px-6">
        {/* Left section */}
        <div className="flex items-center">
          {/* Mobile menu button */}
          <button
            onClick={onMenuClick}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors md:hidden mr-3"
          >
            <Menu size={20} />
          </button>
          
          {/* Logo/Title */}
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
            S.H.A.K.T.I
          </h1>
        </div>

        {/* Center section - Search bar */}
        <div className="hidden md:flex flex-1 max-w-lg mx-8">
          <div className="relative w-full">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by drone..."
              className="block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-300"
            />
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-2 md:space-x-4">
          {/* Mobile search button */}
          <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors md:hidden">
            <Search size={20} className="text-gray-600" />
          </button>

          {/* Add Organization button */}
          <button className="hidden sm:flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 hover:shadow-lg transform hover:scale-105 transition-all duration-200">
            <Plus size={16} className="mr-2" />
            <span className="text-sm font-medium">Add Organization</span>
          </button>

          {/* Add Drone button */}
          <button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 hover:shadow-lg transform hover:scale-105 transition-all duration-200">
            <Plus size={16} className="mr-2" />
            <span className="text-sm font-medium hidden sm:inline">Add Drone</span>
            <span className="text-sm font-medium sm:hidden">Add</span>
          </button>

          {/* Notification bell */}
          <button className="relative p-3 rounded-lg hover:bg-gray-100 transition-all duration-200 hover:shadow-sm">
            <Bell size={20} className="text-gray-600" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium animate-pulse">
              3
            </span>
          </button>
        </div>
      </div>

      {/* Mobile search bar */}
      <div className="px-4 pb-3 md:hidden">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by drone..."
            className="block w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          />
        </div>
      </div>
    </header>
  );
};

export default TopNavbar;
