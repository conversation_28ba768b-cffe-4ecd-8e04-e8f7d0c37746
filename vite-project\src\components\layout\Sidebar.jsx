import React, { useState } from 'react';
import { 
  Home, 
  Map, 
  Building, 
  Package, 
  Rocket, 
  Plane, 
  Bell, 
  LogOut, 
  Menu, 
  X 
} from 'lucide-react';
import { navigationItems } from '../../data/mockData';

const iconMap = {
  home: Home,
  map: Map,
  building: Building,
  package: Package,
  rocket: Rocket,
  drone: Plane,
  bell: Bell
};

const Sidebar = ({ isOpen, setIsOpen }) => {
  const [activeItem, setActiveItem] = useState('Dashboard');

  const handleItemClick = (itemName) => {
    setActiveItem(itemName);
    // Close mobile menu after selection
    if (window.innerWidth < 768) {
      setIsOpen(false);
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed left-0 top-0 h-full w-60 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0 md:static md:z-auto
      `}>
        {/* Mobile close button */}
        <div className="flex justify-end p-4 md:hidden">
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Navigation items */}
        <nav className="px-4 py-6">
          <ul className="space-y-1">
            {navigationItems.map((item) => {
              const IconComponent = iconMap[item.icon];
              const isActive = activeItem === item.name;

              return (
                <li key={item.id}>
                  <button
                    onClick={() => handleItemClick(item.name)}
                    className={`
                      w-full flex items-center px-4 py-3 rounded-lg text-left transition-all duration-200 group
                      ${isActive
                        ? 'bg-blue-50 text-blue-600 shadow-sm'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }
                    `}
                  >
                    <IconComponent
                      size={20}
                      className={`mr-3 flex-shrink-0 ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'}`}
                    />
                    <span className="font-medium text-sm whitespace-nowrap">{item.name}</span>
                    {isActive && (
                      <div className="ml-auto w-1 h-6 bg-blue-600 rounded-full"></div>
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          {/* Company branding */}
          <div className="mb-4 text-center">
            <div className="text-sm font-semibold text-gray-800">RPM Aerospace</div>
            <div className="text-xs text-gray-500">Drone Management System</div>
          </div>
          
          {/* Logout button */}
          <button className="w-full flex items-center px-4 py-3 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200">
            <LogOut size={20} className="mr-3" />
            <span className="font-medium">Logout</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
