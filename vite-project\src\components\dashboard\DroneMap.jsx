import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { MapPin, ChevronDown, Expand } from 'lucide-react';
import { droneLocations, filterOptions } from '../../data/mockData';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom drone icon
const droneIcon = new L.Icon({
  iconUrl: 'data:image/svg+xml;base64,' + btoa(`
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#10b981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12 2L8 6h8l-4-4z"/>
      <path d="M12 22l4-4H8l4 4z"/>
      <path d="M2 12l4-4v8l-4-4z"/>
      <path d="M22 12l-4-4v8l4-4z"/>
      <circle cx="12" cy="12" r="2"/>
    </svg>
  `),
  iconSize: [25, 25],
  iconAnchor: [12, 12],
  popupAnchor: [0, -12],
});

const DroneMap = () => {
  const [selectedFilter, setSelectedFilter] = useState('active');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [filteredDrones, setFilteredDrones] = useState(droneLocations);
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (selectedFilter === 'active') {
      setFilteredDrones(droneLocations);
    } else {
      setFilteredDrones(droneLocations.slice(0, 5)); // Show fewer for other filters
    }
  }, [selectedFilter]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const selectedOption = filterOptions.find(option => option.value === selectedFilter);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <MapPin className="text-blue-600" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Live Drone Tracking</h2>
              <p className="text-sm text-gray-600">Real-time monitoring of active drones</p>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Controls */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
          {/* Filter dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center justify-between w-full sm:w-auto px-4 py-3 bg-white border-2 border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <span className="text-sm font-medium text-gray-700">
                {selectedOption?.label}
              </span>
              <ChevronDown
                size={16}
                className={`ml-3 text-gray-500 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
              />
            </button>

            {isDropdownOpen && (
              <div className="absolute top-full left-0 mt-2 w-full sm:w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-20 overflow-hidden">
                {filterOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => {
                      setSelectedFilter(option.value);
                      setIsDropdownOpen(false);
                    }}
                    className={`w-full px-4 py-3 text-left text-sm hover:bg-blue-50 transition-colors ${
                      selectedFilter === option.value ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                    }`}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Full screen button */}
          <button className="flex items-center px-4 py-3 text-sm font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200">
            <Expand size={16} className="mr-2" />
            View full screen
          </button>
        </div>

        {/* Map container */}
        <div className="h-96 md:h-[450px] rounded-xl overflow-hidden border-2 border-gray-200 shadow-inner">
          <MapContainer
            center={[40.7128, -74.0060]}
            zoom={11}
            style={{ height: '100%', width: '100%' }}
            className="z-0"
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />

            {filteredDrones.map((drone) => (
              <Marker
                key={drone.id}
                position={[drone.lat, drone.lng]}
                icon={droneIcon}
              >
                <Popup>
                  <div className="text-center p-2">
                    <h3 className="font-semibold text-gray-900 mb-1">{drone.name}</h3>
                    <div className="flex items-center justify-center mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <p className="text-sm text-gray-600 capitalize">{drone.status}</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      {drone.lat.toFixed(4)}, {drone.lng.toFixed(4)}
                    </p>
                  </div>
                </Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>
      </div>
    </div>
  );
};

export default DroneMap;
