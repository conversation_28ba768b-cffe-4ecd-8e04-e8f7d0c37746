import React from 'react';
import KPICard from './KPICard';
import { kpiStats } from '../../data/mockData';

const KPIStats = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {kpiStats.map((stat, index) => (
        <div
          key={stat.id}
          className="animate-fade-in-up"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <KPICard
            title={stat.title}
            value={stat.value}
            trend={stat.trend}
            trendPositive={stat.trendPositive}
            icon={stat.icon}
            color={stat.color}
          />
        </div>
      ))}
    </div>
  );
};

export default KPIStats;
