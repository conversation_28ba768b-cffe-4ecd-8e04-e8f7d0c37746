import React from 'react';
import { Plane, Building, Settings, Package } from 'lucide-react';

const iconMap = {
  drone: Plane,
  building: Building,
  plane: Plane,
  settings: Settings,
  package: Package
};

const colorClasses = {
  green: {
    border: 'border-l-green-500',
    bg: 'bg-green-50',
    icon: 'text-green-600',
    trend: 'text-green-600'
  },
  blue: {
    border: 'border-l-blue-500',
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    trend: 'text-blue-600'
  },
  red: {
    border: 'border-l-red-500',
    bg: 'bg-red-50',
    icon: 'text-red-600',
    trend: 'text-red-600'
  },
  yellow: {
    border: 'border-l-yellow-500',
    bg: 'bg-yellow-50',
    icon: 'text-yellow-600',
    trend: 'text-yellow-600'
  }
};

const KPICard = ({ title, value, trend, trendPositive, icon, color }) => {
  const IconComponent = iconMap[icon];
  const colors = colorClasses[color];

  return (
    <div className={`
      bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-lg
      transition-all duration-300 hover:scale-105 cursor-pointer
      relative overflow-hidden
    `}>
      {/* Color accent bar */}
      <div className={`absolute top-0 left-0 right-0 h-1 ${colors.border.replace('border-l-', 'bg-')}`}></div>

      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide">
              {title}
            </h3>
            <div className="flex items-baseline mb-3">
              <p className="text-4xl font-bold text-gray-900">
                {value}
              </p>
            </div>
            <div className={`
              flex items-center text-sm font-medium
              ${trendPositive ? colors.trend : 'text-red-600'}
            `}>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
                {trend}
              </span>
            </div>
          </div>

          <div className={`
            p-4 rounded-xl ${colors.bg} shadow-sm
          `}>
            <IconComponent
              size={28}
              className={colors.icon}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default KPICard;
